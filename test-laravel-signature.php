<?php

// Simulate the Laravel signature validation
function extractSigningKeyFromSecret($webhookSecret)
{
    // This is a guess based on the JWT-style format
    // The actual implementation might be different

    // If it's a JWT-style token, extract the payload part
    if (strpos($webhookSecret, 'wh_') === 0) {
        $parts = explode('.', $webhookSecret);
        if (count($parts) >= 2) {
            // Decode the payload part (second part after 'wh_')
            $payload = base64_decode($parts[1]);
            $decoded = json_decode($payload, true);
            // Use the 't' field as the signing key (team ID)
            return $decoded['t'] ?? $webhookSecret;
        }
    }

    // If it's a simple secret, return as-is
    return $webhookSecret;
}

function validateSignature($payload, $signature, $signingKey)
{
    // Parse the signature (Stripe-style format: t=timestamp,v1=signature)
    if (!preg_match('/t=(\d+),v1=([^,]+)/', $signature, $matches)) {
        return false;
    }

    $timestamp = (int) $matches[1];
    $providedSignature = $matches[2];

    // Check if timestamp is within acceptable range (5 minutes)
    $currentTime = time();
    if (abs($currentTime - $timestamp) > 300) {
        return false;
    }

    // Recreate the signed payload (timestamp.payload)
    $signedPayload = $timestamp . '.' . $payload;

    // Generate expected signature
    $expectedSignature = hash_hmac('sha256', $signedPayload, $signingKey);

    return hash_equals($expectedSignature, $providedSignature);
}

// Test with the actual webhook secret and signature from the deployed Worker
$webhookSecret = "wh_f1be34ea3bff.eyJ0IjoiMDFqcTcwam5zdHlmemV2YzY0MjNjemg1MGUiLCJwIjoiaW50YWtlLWZvcm0ifQ==";
$extractedKey = extractSigningKeyFromSecret($webhookSecret);

echo "Webhook Secret: " . $webhookSecret . "\n";
echo "Extracted Signing Key: " . $extractedKey . "\n";

// Test payload (exact same as Worker)
$testPayload = '{"event":"contact_form","timestamp":"2025-07-16T05:09:20.305Z","teamId":"north-carolina-legal-services","test":true,"data":{"message":"This is a test webhook payload"}}';

// Signature generated by the deployed Worker
$workerSignature = "t=1752642584,v1=4aac70b6e53d07268a611883966e752abba2659df8388b5160787cfd6eff5a17";

echo "\nTest Payload: " . $testPayload . "\n";
echo "Worker Signature: " . $workerSignature . "\n";

// Test validation
$isValid = validateSignature($testPayload, $workerSignature, $extractedKey);
echo "Signature Valid: " . ($isValid ? "YES" : "NO") . "\n";

// Also test with raw secret
$isValidRaw = validateSignature($testPayload, $workerSignature, $webhookSecret);
echo "Signature Valid (raw secret): " . ($isValidRaw ? "YES" : "NO") . "\n";

// Let's also test what the expected signature should be
$timestamp = 1752642584;
$signedPayload = $timestamp . '.' . $testPayload;
$expectedSignature = hash_hmac('sha256', $signedPayload, $extractedKey);
echo "Expected Signature: " . $expectedSignature . "\n";
echo "Provided Signature: 4aac70b6e53d07268a611883966e752abba2659df8388b5160787cfd6eff5a17\n";
echo "Match: " . (hash_equals($expectedSignature, "4aac70b6e53d07268a611883966e752abba2659df8388b5160787cfd6eff5a17") ? "YES" : "NO") . "\n";
