import crypto from 'crypto';

// Extract signing key from webhook secret (matches <PERSON><PERSON> logic)
function extractSigningKeyFromSecret(webhookSecret) {
  // If it's a JWT-style token (starts with wh_), extract the actual signing key
  if (webhookSecret.startsWith('wh_')) {
    const parts = webhookSecret.split('.');
    if (parts.length >= 2) {
      try {
        // Decode the payload part (second part after 'wh_')
        const payload = Buffer.from(parts[1], 'base64').toString();
        const decoded = JSON.parse(payload);
        // Use the 't' field as the signing key (team ID)
        return decoded.t || webhookSecret;
      } catch (error) {
        console.warn('Failed to extract signing key from webhook secret:', error);
        return webhookSecret;
      }
    }
  }
  
  // If it's a simple secret, return as-is
  return webhookSecret;
}

// Test webhook signature generation
async function generateHMACSHA256(message, key) {
  const encoder = new TextEncoder();
  const messageBytes = encoder.encode(message);
  const keyBytes = encoder.encode(key);
  
  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyBytes,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign(
    'HMAC',
    cryptoKey,
    messageBytes,
  );

  // Convert to hex string
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

async function generateWebhookSignature(payload, signingKey, timestamp = null) {
  // Use current timestamp if not provided
  if (timestamp === null || timestamp === undefined) {
    timestamp = Math.floor(Date.now() / 1000);
  }

  // Create signed payload (timestamp.payload)
  const signedPayload = `${timestamp}.${payload}`;

  // Generate HMAC-SHA256 signature
  const signature = await generateHMACSHA256(signedPayload, signingKey);

  // Return in Stripe-like format
  return `t=${timestamp},v1=${signature}`;
}

// Test with the actual values from teams.json
const testPayload = JSON.stringify({
  event: 'contact_form',
  timestamp: '2025-07-16T04:55:59.276Z',
  teamId: 'north-carolina-legal-services',
  test: true,
  data: {
    message: 'This is a test webhook payload'
  }
});

const secret = 'wh_f1be34ea3bff.eyJ0IjoiMDFqcTcwam5zdHlmemV2YzY0MjNjemg1MGUiLCJwIjoiaW50YWtlLWZvcm0ifQ==';
const timestamp = Math.floor(Date.now() / 1000);

// Extract the actual signing key
const signingKey = extractSigningKeyFromSecret(secret);

generateWebhookSignature(testPayload, signingKey, timestamp).then(signature => {
  console.log('Test Payload:', testPayload);
  console.log('Original Secret:', secret);
  console.log('Extracted Signing Key:', signingKey);
  console.log('Timestamp:', timestamp);
  console.log('Generated Signature:', signature);
  console.log('Signed Payload:', `${timestamp}.${testPayload}`);
}); 