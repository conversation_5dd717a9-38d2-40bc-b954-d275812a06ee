<?php

// Simulate the Laravel signature validation
function extractSigningKeyFromSecret($webhookSecret)
{
    // If the secret is in the signed format (wh_<signature>.<payload>)
    if (strpos($webhookSecret, 'wh_') === 0) {
        $parts = explode('.', $webhookSecret);
        if (count($parts) === 2) {
            // Use the signature part as the signing key
            $signature = str_replace('wh_', '', $parts[0]);
            return $signature;
        }
    }

    // If it's a simple string, use it directly
    return $webhookSecret;
}

function validateSignature($payload, $signature, $signingKey)
{
    // Parse the signature (Stripe-style format: t=timestamp,v1=signature)
    if (!preg_match('/t=(\d+),v1=([^,]+)/', $signature, $matches)) {
        return false;
    }

    $timestamp = (int) $matches[1];
    $providedSignature = $matches[2];

    // Check if timestamp is within acceptable range (5 minutes)
    $currentTime = time();
    if (abs($currentTime - $timestamp) > 300) {
        return false;
    }

    // Recreate the signed payload (timestamp.payload)
    $signedPayload = $timestamp . '.' . $payload;

    // Generate expected signature
    $expectedSignature = hash_hmac('sha256', $signedPayload, $signingKey);

    return hash_equals($expectedSignature, $providedSignature);
}

// Test with the actual webhook secret and signature from the updated Worker
$webhookSecret = "wh_f1be34ea3bff.eyJ0IjoiMDFqcTcwam5zdHlmemV2YzY0MjNjemg1MGUiLCJwIjoiaW50YWtlLWZvcm0ifQ==";
$extractedKey = extractSigningKeyFromSecret($webhookSecret);

echo "Webhook Secret: " . $webhookSecret . "\n";
echo "Extracted Signing Key: " . $extractedKey . "\n";

// Test payload (exact same as Worker)
$testPayload = '{"event":"contact_form","timestamp":"2025-07-16T05:10:09.360Z","teamId":"north-carolina-legal-services","test":true,"data":{"message":"This is a test webhook payload"}}';

// Signature generated by the updated Worker
$workerSignature = "t=1752642729,v1=2887f49faf6a06af91d6fa8073cf62ec471658ab37c8c25e8cc611b0834fa241";

echo "\nTest Payload: " . $testPayload . "\n";
echo "Worker Signature: " . $workerSignature . "\n";

// Test validation
$isValid = validateSignature($testPayload, $workerSignature, $extractedKey);
echo "Signature Valid: " . ($isValid ? "YES" : "NO") . "\n";

// Let's also test what the expected signature should be
$timestamp = 1752642729;
$signedPayload = $timestamp . '.' . $testPayload;
$expectedSignature = hash_hmac('sha256', $signedPayload, $extractedKey);
echo "Expected Signature: " . $expectedSignature . "\n";
echo "Provided Signature: 2887f49faf6a06af91d6fa8073cf62ec471658ab37c8c25e8cc611b0834fa241\n";
echo "Match: " . (hash_equals($expectedSignature, "2887f49faf6a06af91d6fa8073cf62ec471658ab37c8c25e8cc611b0834fa241") ? "YES" : "NO") . "\n";
